# EventEndPopup 活动结束弹窗使用说明

## 功能概述

EventEndPopup 组件实现了活动状态自动判断和弹窗触发功能，支持以下两个关键阶段：

1. **预下载阶段**：活动结束前8小时开始，显示预下载相关内容
2. **活动结束阶段**：活动正式结束后，显示活动结束相关内容

## 核心特性

### 自动状态判断
- 基于服务器时间进行精确的时间比较
- 自动计算预下载开始时间（活动结束时间 - 8小时）
- 实时监控活动状态变化

### 智能弹窗触发
- 首次进入预下载阶段时自动弹出
- 首次进入活动结束阶段时自动弹出
- 避免重复弹窗，每个阶段只触发一次

### 视觉状态切换
- 预下载阶段：`isOnline = false`，对应CSS类名 `pre`
- 活动结束阶段：`isOnline = true`，对应CSS类名 `online`

## 数据依赖

组件依赖以下数据源：

```typescript
// 从 @kingnet/kingnet-pre-test 获取
const { warmPageInfo, businessConfig } = storeToRefs(store);

// 必需数据
warmPageInfo.value.end_time      // 活动结束时间（秒级时间戳）
businessConfig.value.serverTime  // 服务器当前时间（秒级时间戳）
```

## 时间计算逻辑

### 预下载阶段判断
```typescript
const PRE_DOWNLOAD_DURATION = 8 * 60 * 60; // 8小时 = 28800秒
const preDownloadStartTime = endTime - PRE_DOWNLOAD_DURATION;

// 预下载阶段：[preDownloadStartTime, endTime)
if (currentServerTime >= preDownloadStartTime && currentServerTime < endTime) {
  // 触发预下载弹窗
}
```

### 活动结束判断
```typescript
// 活动结束阶段：currentServerTime >= endTime
if (currentServerTime >= endTime) {
  // 触发活动结束弹窗
}
```

## 使用方式

### 1. 在页面中引入组件
```vue
<template>
  <!-- 活动结束弹窗 -->
  <EventEndPopup ref="eventEndPopRef" />
</template>

<script setup lang="ts">
import EventEndPopup from "@/components/popup/EventEndPopup.vue";

const eventEndPopRef = ref<InstanceType<typeof EventEndPopup> | null>(null);
</script>
```

### 2. 手动触发弹窗（可选）
```typescript
// 手动打开弹窗
eventEndPopRef.value?.open();
```

## 监控机制

### 自动监控
- 组件挂载时自动开始监控
- 每30秒检查一次活动状态
- 数据变化时重新启动监控

### 监控日志
组件会在控制台输出详细的状态检查信息：
```javascript
console.log('活动状态检查:', {
  status,                    // 当前状态：'active' | 'pre-download' | 'ended'
  endTime,                   // 活动结束时间
  currentServerTime,         // 当前服务器时间
  preDownloadStartTime,      // 预下载开始时间
  isOnline,                  // 是否为线上状态
  hasShownPreDownloadPopup,  // 是否已显示预下载弹窗
  hasShownEndPopup          // 是否已显示结束弹窗
});
```

## 测试工具

项目提供了测试工具来验证逻辑：

```typescript
import { generateTestCases } from '@/utils/eventEndTest';

// 在控制台运行测试用例
generateTestCases();
```

## 注意事项

1. **时间精度**：所有时间戳都是秒级精度，确保数据一致性
2. **服务器时间**：使用服务器时间避免客户端时间不准确的问题
3. **单次触发**：每个阶段的弹窗只会触发一次，避免重复打扰用户
4. **资源清理**：组件卸载时会自动清理定时器，避免内存泄漏

## 边界情况处理

- 数据未加载时不会触发弹窗
- 时间计算异常时返回安全的默认状态
- 组件重新挂载时会重置弹窗显示状态
