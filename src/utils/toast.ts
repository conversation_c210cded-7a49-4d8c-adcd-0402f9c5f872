import { createApp, type App } from "vue";
import Toast from "@/components/popup/Toast.vue";

interface ToastOptions {
  message: string;
  duration?: number;
  clickToClose?: boolean;
}

let toastInstance: App | null = null;

export function showToast(options: string | ToastOptions) {
  // 如果已有实例，先销毁
  if (toastInstance) {
    toastInstance.unmount();
    toastInstance = null;
  }

  // 处理参数
  const config = typeof options === "string" ? { message: options } : options;

  // 创建容器
  const container = document.createElement("div");
  document.body.appendChild(container);

  // 创建应用实例
  toastInstance = createApp(Toast, {
    ...config,
    onClose: () => {
      // 组件关闭后清理
      setTimeout(() => {
        if (toastInstance) {
          toastInstance.unmount();
          toastInstance = null;
        }
        if (container.parentNode) {
          container.parentNode.removeChild(container);
        }
      }, 300); // 等待动画完成
    },
  });

  // 挂载到容器
  toastInstance.mount(container);

  return {
    close: () => {
      if (toastInstance) {
        toastInstance.unmount();
        toastInstance = null;
        if (container.parentNode) {
          container.parentNode.removeChild(container);
        }
      }
    },
  };
}

// 便捷方法
export const toast = {
  show: showToast,
  success: (message: string) => showToast({ message, duration: 2000 }),
  error: (message: string) => showToast({ message, duration: 4000 }),
  info: (message: string) => showToast({ message, duration: 3000 }),
  warning: (message: string) => showToast({ message, duration: 3500 }),
};
