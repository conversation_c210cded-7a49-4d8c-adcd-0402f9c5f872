/**
 * 活动结束弹窗测试工具
 * 用于测试活动状态判断逻辑
 */

// 预下载阶段时间（8小时 = 28800秒）
const PRE_DOWNLOAD_DURATION = 8 * 60 * 60;

/**
 * 测试活动状态判断逻辑
 * @param endTime 活动结束时间（秒级时间戳）
 * @param currentServerTime 当前服务器时间（秒级时间戳）
 */
export function testActivityStatus(endTime: number, currentServerTime: number) {
  const preDownloadStartTime = endTime - PRE_DOWNLOAD_DURATION;
  
  let status: string;
  let isOnline: boolean;
  
  if (currentServerTime >= preDownloadStartTime && currentServerTime < endTime) {
    // 预下载阶段
    status = 'pre-download';
    isOnline = false;
  } else if (currentServerTime >= endTime) {
    // 活动结束阶段
    status = 'ended';
    isOnline = true;
  } else {
    // 活动进行中
    status = 'active';
    isOnline = false;
  }
  
  return {
    status,
    isOnline,
    endTime,
    currentServerTime,
    preDownloadStartTime,
    timeToPreDownload: preDownloadStartTime - currentServerTime,
    timeToEnd: endTime - currentServerTime,
    debug: {
      endTimeDate: new Date(endTime * 1000).toLocaleString(),
      currentServerTimeDate: new Date(currentServerTime * 1000).toLocaleString(),
      preDownloadStartTimeDate: new Date(preDownloadStartTime * 1000).toLocaleString(),
    }
  };
}

/**
 * 生成测试用例
 */
export function generateTestCases() {
  const now = Math.floor(Date.now() / 1000); // 当前时间戳（秒）
  const endTime = now + 24 * 60 * 60; // 24小时后结束
  
  const testCases = [
    {
      name: '活动进行中（距离预下载还有12小时）',
      endTime,
      currentServerTime: now,
    },
    {
      name: '预下载阶段开始（距离结束还有8小时）',
      endTime,
      currentServerTime: endTime - PRE_DOWNLOAD_DURATION,
    },
    {
      name: '预下载阶段中期（距离结束还有4小时）',
      endTime,
      currentServerTime: endTime - PRE_DOWNLOAD_DURATION / 2,
    },
    {
      name: '预下载阶段即将结束（距离结束还有1分钟）',
      endTime,
      currentServerTime: endTime - 60,
    },
    {
      name: '活动刚结束',
      endTime,
      currentServerTime: endTime,
    },
    {
      name: '活动结束1小时后',
      endTime,
      currentServerTime: endTime + 60 * 60,
    },
  ];
  
  console.log('=== 活动状态测试用例 ===');
  testCases.forEach((testCase, index) => {
    console.log(`\n${index + 1}. ${testCase.name}`);
    const result = testActivityStatus(testCase.endTime, testCase.currentServerTime);
    console.log('结果:', result);
  });
}

// 在开发环境下自动运行测试
if (import.meta.env.DEV) {
  // 延迟执行，确保在控制台中能看到
  setTimeout(() => {
    console.log('EventEndPopup 测试工具已加载，可以调用 generateTestCases() 进行测试');
    // generateTestCases();
  }, 1000);
}
