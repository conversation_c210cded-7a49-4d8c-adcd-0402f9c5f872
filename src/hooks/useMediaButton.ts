import { useDeviceDetection } from "./useDeviceDetection";
import { getUseAppStore, type ToLinkCode } from "@kingnet/kingnet-pre-test";

export function useMediaButton() {
  const store = getUseAppStore()();
  const { isIOS } = useDeviceDetection();

  /**
   * 商店按钮点击事件
   * @param eventName 事件名称(无eventName则跳转通用商店)
   * @param code 商店代码(无code则跳转通用商店)
   */
  const toStore = (eventName: string, code?: ToLinkCode) => {
    let allEventName = "";

    // 确定通用事件名称
    if (eventName) {
      if (eventName.includes("ios")) {
        allEventName = "iosall";
      } else if (eventName.includes("gp")) {
        allEventName = "gpall";
      }
    } else {
      allEventName = isIOS.value ? "iosall" : "gpall";
    }

    // 通用商店事件
    store.ga4TrackEvent(allEventName);
    // 特定商店事件
    eventName && store.ga4TrackEvent(eventName);
    // 跳转指定商店(无code则跳转通用商店)
    store.toLink({ code: code ? code : isIOS.value ? "ios" : "gp" });
  };

  /**
   * 社群按钮点击事件
   * @param eventName 事件名称
   */
  const toCommunity = (eventName: string) => {
    // 通用社群事件
    store.ga4TrackEvent("loungeall");
    // 特定社群事件
    eventName && store.ga4TrackEvent(eventName);
    // 跳转社群
    store.toLink({ code: "community" });
  };

  return {
    toStore,
    toCommunity,
  };
}
