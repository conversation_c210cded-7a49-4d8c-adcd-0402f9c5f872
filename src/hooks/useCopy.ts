import { ref } from "vue";

interface UseCopyOptions {
  onSuccess?: (text: string) => void;
  onError?: (error: Error) => void;
  successMessage?: string;
}

export function useCopy(options: UseCopyOptions = {}) {
  const isSupported = ref(false);
  const isCopying = ref(false);
  const hasCopied = ref(false);

  // 检查浏览器是否支持复制功能
  const checkSupport = () => {
    if (typeof navigator !== "undefined") {
      isSupported.value = !!(navigator.clipboard || document.execCommand);
    }
    return isSupported.value;
  };

  // 复制文本到剪贴板
  const copy = async (text: string) => {
    if (!text || isCopying.value) return false;

    isCopying.value = true;
    hasCopied.value = false;

    try {
      // 优先使用现代的 Clipboard API
      if (navigator.clipboard) {
        await navigator.clipboard.writeText(text);
      } else {
        // 降级使用 execCommand (兼容老浏览器)
        const textArea = document.createElement("textarea");
        textArea.value = text;
        textArea.style.position = "fixed";
        textArea.style.left = "-999999px";
        textArea.style.top = "-999999px";
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        const successful = document.execCommand("copy");
        document.body.removeChild(textArea);

        if (!successful) {
          throw new Error("复制失败");
        }
      }

      hasCopied.value = true;
      options.onSuccess?.(text);

      // 3秒后重置状态
      setTimeout(() => {
        hasCopied.value = false;
      }, 3000);

      return true;
    } catch (error) {
      const err = error instanceof Error ? error : new Error("复制失败");
      options.onError?.(err);
      return false;
    } finally {
      isCopying.value = false;
    }
  };

  // 初始化检查支持性
  checkSupport();

  return {
    isSupported: isSupported.value,
    isCopying,
    hasCopied,
    copy,
  };
}
