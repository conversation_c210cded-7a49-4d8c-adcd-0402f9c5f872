import { type InitConfig } from "@kingnet/kingnet-pre-test";

export const platformMap: InitConfig["platformMap"] = {
  // FB渠道
  reserve_FB: {
    channel: "meta",
    eventName: "CompleteRegistration",
    id: "775642494943173",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts1_FB: {
    channel: "meta",
    eventName: "CompleteRegistration",
    id: "775642494943173",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts2_FB: {
    channel: "meta",
    eventName: "CompleteRegistration",
    id: "775642494943173",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts3_FB: {
    channel: "meta",
    eventName: "CompleteRegistration",
    id: "775642494943173",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  // Google渠道
  reserve_GG1: {
    channel: "ga4Utm",
    id: "AW-11070103320",
    eventName: "conversion",
    params: {
      send_to: "AW-11070103320/TgODCOj01o4bEJi-0Z4p",
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  reserve_search_GG1: {
    channel: "ga4Utm",
    id: "AW-11070103320",
    eventName: "conversion",
    params: {
      send_to: "AW-11070103320/xH5tCNvOx44bEJi-0Z4p",
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  reserve_GG2: {
    channel: "ga4Utm",
    id: "AW-11285958617",
    eventName: "conversion",
    params: {
      send_to: "AW-11285958617/tsyGCNHO1o4bENmfyIUq",
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts1_GG: {
    channel: "ga4Utm",
    id: "AW-11285958617",
    eventName: "conversion",
    params: {
      send_to: "AW-11285958617/7hVNCPPsx44bENmfyIUq",
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts2_GG: {
    channel: "ga4Utm",
    id: "AW-11285958617",
    eventName: "conversion",
    params: {
      send_to: "AW-11285958617/yVwTCJz4x44bENmfyIUq",
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts3_GG: {
    channel: "ga4Utm",
    id: "AW-11285958617",
    eventName: "conversion",
    params: {
      send_to: "AW-11285958617/PJdrCJbay44bENmfyIUq",
    },
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  // TikTok渠道
  reserve_TT: {
    channel: "tiktok",
    id: "D2NA2SBC77U0VILIIT40",
    eventName: "ClickButton",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  reserve_PG: {
    channel: "tiktok",
    id: "D2NA4JRC77U676SVL2E0",
    eventName: "ClickButton",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts1_TT: {
    channel: "tiktok",
    id: "D2NA51JC77U9J62CBCBG",
    eventName: "ClickButton",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts2_TT: {
    channel: "tiktok",
    id: "D2NA5KJC77U2SIGRQUJ0",
    eventName: "ClickButton",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  shorts3_TT: {
    channel: "tiktok",
    id: "D2NA61BC77U9E0P21I40",
    eventName: "ClickButton",
    gp_link: "https://play.google.com/store/apps/details?id=com.zy.gpkr",
    ios_cpp_link: "",
  },
  //通用Ga4
  common_ga4: {
    channel: "ga4",
    eventName: "",
    id: "G-MGB9CGG47E",
  },
};
