import "./assets/main.css";
// 引入私有包的样式文件
import "@kingnet/kingnet-pre-test/dist/style.css";

import { createApp } from "vue";
import { createPinia } from "pinia";
import PiniaPersistedState from "pinia-plugin-persistedstate";
import App from "./App.vue";
import router from "./router";

const app = createApp(App);

const pinia = createPinia();

// 使用持久化插件
pinia.use(PiniaPersistedState);

app.use(pinia);
app.use(router);

app.mount("#app");
