<template>
  <div class="countdown-container">
    <span class="time-unit hours">{{ formatTime(hours) }}</span>
    <span class="separator">:</span>
    <span class="time-unit minutes">{{ formatTime(minutes) }}</span>
    <span class="separator">:</span>
    <span class="time-unit seconds">{{ formatTime(seconds) }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 响应式数据
const totalSeconds = ref(30)
const hours = ref(0)
const minutes = ref(0)
const seconds = ref(30)
const timer = ref<number | null | NodeJS.Timeout>(null)

// 格式化时间为两位数
const formatTime = (time: number): string => {
  return time.toString().padStart(2, '0')
}

// 更新时分秒显示
const updateTimeDisplay = () => {
  const total = totalSeconds.value
  hours.value = Math.floor(total / 3600)
  minutes.value = Math.floor((total % 3600) / 60)
  seconds.value = total % 60
}

// 开始倒计时
const startCountdown = () => {
  // 清除现有定时器
  if (timer.value) {
    clearInterval(timer.value)
  }

  timer.value = setInterval(() => {
    totalSeconds.value--

    // 更新显示
    updateTimeDisplay()

    // 当倒计时归零时，重新开始30秒倒计时
    if (totalSeconds.value <= 0) {
      totalSeconds.value = 30
      updateTimeDisplay()
    }
  }, 1000)
}

// 停止倒计时
const stopCountdown = () => {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = null
  }
}

// 重置倒计时
const resetCountdown = () => {
  totalSeconds.value = 30
  updateTimeDisplay()
}

// 组件挂载时自动开始倒计时
onMounted(() => {
  updateTimeDisplay()
  startCountdown()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopCountdown()
})

// 暴露方法供外部调用（可选）
defineExpose({
  startCountdown,
  stopCountdown,
  resetCountdown
})
</script>

<style lang="less" scoped>
.countdown-container {
  display: inline-flex;
  align-items: center;
  font-family: 'SimHei', 'Arial', monospace;
  font-size: 40px;
  pointer-events: none;
  user-select: none;
}

.time-unit {
  color: #efb8ff;
  text-align: center;
  border: 1px solid #564e7b;
  padding: 2px 50px;
}

.separator {
  color: #efb8ff;
  margin: 0 8px;
  font-weight: bold;
}

@media (max-width: 540px) {
  .countdown-container {
    font-size: 0.6rem;
  }

  .time-unit {
    padding: 0.02rem 0.35rem;
  }

  .separator {
    margin: 0 0.05rem;
  }
}
</style>
