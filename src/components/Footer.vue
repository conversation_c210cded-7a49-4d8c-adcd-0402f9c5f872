<template>
  <div class="footer-container">
    <div class="footer-content">
      <div class="prize-section">
        <img src="@/assets/imgs/footer/prize.png" class="prize-image breathe" />
      </div>

      <div class="store-section">
        <WebM class="store-button breathe" :src="storeButtonSrc" @click="handleStoreClick" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import WebM from '@/components/WebM.vue'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { getUseAppStore } from "@kingnet/kingnet-pre-test";

import WebMAppleStore from '@/assets/imgs/webm/apple-store.webm'
import WebMGooglePlay from '@/assets/imgs/webm/google-play.webm'

const store = getUseAppStore()()

const { isIOS } = useDeviceDetection()

const storeButtonSrc = computed(() => {
  return isIOS.value ? WebMAppleStore : WebMGooglePlay
})

const handleStoreClick = () => {
  store.toLink({ code: 'store' })
}
</script>

<style lang="less" scoped>
.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  pointer-events: none;

  .footer-content {
    width: 7.5rem;
    height: 2.03rem;
    margin: 0 auto;
    background: url("@/assets/imgs/footer/bg.png") no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    pointer-events: auto;

    .prize-section {
      .prize-image {
        width: 3.5rem;
        height: auto;
      }
    }

    .store-section {
      margin-top: .75rem;

      .store-button {
        width: 2.6rem;
        height: auto;
        cursor: pointer;
      }
    }
  }
}
</style>