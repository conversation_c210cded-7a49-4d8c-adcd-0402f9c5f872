<template>
  <div class="horizontal-swiper-container">
    <BaseSwiper direction="horizontal" :slides-per-view="1" :space-between="0" :speed="1000" :loop="true"
      :autoplay="{ delay: 4000, disableOnInteraction: false }" :navigation="true" :modules="modules" effect="fade"
      @swiper-ready="onSwiperReady" @slide-change="onSlideChange">
      <swiper-slide v-for="(image, index) in backgroundImages" :key="index" class="horizontal-slide"
        :style="{ backgroundImage: `url(${image})` }" />
    </BaseSwiper>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import BaseSwiper from './BaseSwiper.vue'
import { SwiperSlide } from 'swiper/vue'
import { useSwiper } from '@/hooks/useSwiper'
import { useDeviceDetection } from '@/hooks/useDeviceDetection'
import { Navigation, Autoplay, EffectFade } from 'swiper/modules'

// PC端背景图片
import p7_1_1 from '@/assets/imgs/bg/p7-1-1.jpg'
import p7_1_2 from '@/assets/imgs/bg/p7-1-2.jpg'
import p7_1_3 from '@/assets/imgs/bg/p7-1-3.jpg'
import p7_1_4 from '@/assets/imgs/bg/p7-1-4.jpg'
import p7_1_5 from '@/assets/imgs/bg/p7-1-5.jpg'

// 移动端背景图片
import p7_1_1_m from '@/assets/imgs/bg/m/p7-1-1.jpg'
import p7_1_2_m from '@/assets/imgs/bg/m/p7-1-2.jpg'
import p7_1_3_m from '@/assets/imgs/bg/m/p7-1-3.jpg'
import p7_1_4_m from '@/assets/imgs/bg/m/p7-1-4.jpg'
import p7_1_5_m from '@/assets/imgs/bg/m/p7-1-5.jpg'

const emit = defineEmits<{
  slideChange: [index: number]
  swiperReady: [swiper: any]
}>()

// 设备检测
const { isMobile } = useDeviceDetection()

// 背景图片配置
const pcImages = [p7_1_1, p7_1_2, p7_1_3, p7_1_4, p7_1_5]
const mobileImages = [p7_1_1_m, p7_1_2_m, p7_1_3_m, p7_1_4_m, p7_1_5_m]

// 根据设备类型选择背景图片
const backgroundImages = computed(() => {
  return isMobile.value ? mobileImages : pcImages
})

// 使用基础 Swiper Hook
const {
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay,
  modules
} = useSwiper([Navigation, Autoplay, EffectFade])

// Swiper 初始化回调
const onSwiperReady = (swiper: any) => {
  emit('swiperReady', swiper)
}

// 滑动变化回调
const onSlideChange = (swiper: any) => {
  emit('slideChange', swiper.activeIndex)
}

// 暴露方法给父组件
defineExpose({
  swiperInstance,
  currentSlide,
  isReady,
  goToSlide,
  startAutoplay,
  stopAutoplay
})
</script>

<style lang="less" scoped>
.horizontal-swiper-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.horizontal-swiper {
  width: 100%;
  height: 100%;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.horizontal-slide {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100vw;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  width: 83px;
  height: 85px;
  z-index: 50;
  background: url(@/assets/imgs/p7/tab1-arrow.png);

  &:hover {
    background: url(@/assets/imgs/p7/tab1-arrow.png);
  }

  &:after {
    display: none;
  }
}

:deep(.swiper-button-prev) {
  transform: rotate(180deg);
  left: 18%;
}

:deep(.swiper-button-next) {
  right: 18%;
}

// 移动端响应式适配
@media (max-width: 540px) {
  :deep(.horizontal-slide) {
    background-size: contain;
    background-position: top;
  }

  :deep(.swiper-button-next),
  :deep(.swiper-button-prev) {
    display: none;
  }
}
</style>
