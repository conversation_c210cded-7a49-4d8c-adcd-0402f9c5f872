<template>
  <div class="bg">
    <div class="code">
      <input type="text" v-model="codeText" readonly>
      <div class="copy-btn" @click="handleCopy"></div>
    </div>
    <div class="close" @click="handleClose">
      <img src="@/assets/imgs/pop/order-community/close.png">
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { getUseAppStore, AppUtils } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from '@/config/prizeConfig'

const emit = defineEmits<{
  close: [];
}>();

const store = getUseAppStore()();
const { orderResult } = storeToRefs(store);

const codeText = computed(() => {
  return orderResult.value?.packages.find((item: any) => item.name === prizeConfig.zykr_community_growup.name)?.package_num
})

const handleCopy = () => {
  AppUtils.copy(codeText.value)
}

const handleClose = () => {
  emit('close')
}
</script>

<style scoped lang="less">
.bg {
  position: relative;
  display: flex;
  justify-content: center;
  width: 698px;
  height: 568px;
  background: url("@/assets/imgs/pop/order-community/bg.png") no-repeat;
  background-size: 100% 100%;

  .code {
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    top: 118px;
    right: 100px;
    height: 35px;
    gap: 15px;

    input {
      width: 185px;
      padding: 0 10px;
      color: #fff;
      text-indent: 0;
      font-size: 18px;
      text-align: center;
      background: none;
    }

    .copy-btn {
      width: 98px;
      height: 39px;
      background: url("@/assets/imgs/pop/copy-btn.png") no-repeat 100%/100%;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        filter: brightness(1.1);
      }

      &.copied {
        filter: brightness(1.2) drop-shadow(0 0 5px #4CAF50);
      }
    }
  }

  .close {
    position: absolute;
    bottom: 60px;
    left: 50%;
    width: 216px;
    height: 63px;
    cursor: pointer;
    transform: translateX(-50%);
  }
}

@media (max-width: 540px) {
  .bg {
    width: 6.98rem;
    height: 5.68rem;

    .code {
      top: 1.18rem;
      right: 1rem;
      height: 0.35rem;
      gap: 0.15rem;

      input {
        width: 1.85rem;
        padding: 0 0.1rem;
        font-size: 0.18rem;
      }

      .copy-btn {
        width: 0.98rem;
        height: 0.39rem;
      }
    }

    .close {
      bottom: 0.6rem;
      width: 2.16rem;
      height: 0.63rem;
    }
  }
}
</style>
