<template>
  <Teleport to="body">
    <Transition name="toast" appear>
      <div v-if="visible" class="toast-overlay" @click="handleClose">
        <div class="toast-content" @click.stop>
          <div class="toast-message">{{ message }}</div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

interface ToastProps {
  message: string
  duration?: number  // 自动关闭时间（毫秒），0表示不自动关闭
  clickToClose?: boolean  // 是否点击关闭
}

const props = withDefaults(defineProps<ToastProps>(), {
  duration: 3000,
  clickToClose: true
})

const visible = ref(false)
let timer: NodeJS.Timeout | null = null

const show = () => {
  visible.value = true

  // 设置自动关闭
  if (props.duration > 0) {
    timer = setTimeout(() => {
      handleClose()
    }, props.duration)
  }
}

const handleClose = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
}

onMounted(() => {
  show()
})

onUnmounted(() => {
  if (timer) {
    clearTimeout(timer)
  }
})

defineExpose({
  show,
  close: handleClose
})
</script>

<style scoped lang="less">
.toast-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  cursor: pointer;
}

.toast-content {
  max-width: 80%;
  min-width: 200px;
  padding: 16px 24px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  backdrop-filter: blur(10px);
  cursor: default;

  .toast-message {
    color: #ffffff;
    font-size: 16px;
    line-height: 1.5;
    text-align: center;
    word-break: break-word;
  }
}

// Toast动画
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from,
.toast-leave-to {
  opacity: 0;
  transform: scale(0.9);
}

.toast-enter-to,
.toast-leave-from {
  opacity: 1;
  transform: scale(1);
}

// 移动端适配
@media (max-width: 540px) {
  .toast-content {
    max-width: 90%;
    min-width: 2rem;
    padding: 0.16rem 0.24rem;
    border-radius: 0.08rem;

    .toast-message {
      font-size: 0.16rem;
    }
  }
}
</style>
