<template>
  <Popup ref="popup" :content-top="contentTop">
    <div class="bg" :class="isOnline ? 'online' : 'pre'">
      <div class="btn close"></div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref } from "vue";
import Popup from "./Popup.vue";
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";

const popup = ref<InstanceType<typeof Popup> | null>(null);
const isOnline = ref(false);
const store = getUseAppStore()();
const { warmPageInfo, serverTime } = storeToRefs(store);

console.log(warmPageInfo.value.end_time);

const contentTop = ref("38%");

const open = (endTime: number) => {
  popup.value?.open();
};

defineExpose({
  open,
});
</script>

<style scoped lang="less">
.bg {
  display: flex;
  justify-content: center;
  width: 701px;
  height: 523px;
  background: no-repeat 100%/100%;

  &.pre {
    background-image: url("@/assets/imgs/pop/event-end/pre-bg.png");
  }

  &.online {
    background-image: url("@/assets/imgs/pop/event-end/online-bg.png");
  }

  .btn {
    position: absolute;
    bottom: -15%;
    width: 264px;
    height: 80px;
    background: url("@/assets/imgs/pop/event-end/btn.png") no-repeat 100%/100%;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #81638a);
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 7.01rem;
    height: 5.23rem;

    .btn {
      width: 2.64rem;
      height: 0.8rem;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}
</style>
