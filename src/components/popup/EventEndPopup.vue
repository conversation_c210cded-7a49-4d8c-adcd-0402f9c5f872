<template>
  <Popup ref="popup" :content-top="contentTop">
    <div class="bg" :class="isOnline ? 'online' : 'pre'">
      <div class="btn close" @click="handleClose"></div>
    </div>
  </Popup>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from "vue";
import Popup from "./Popup.vue";
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";

const popup = ref<InstanceType<typeof Popup> | null>(null);
const isOnline = ref(false);
const store = getUseAppStore()();
const { warmPageInfo, businessConfig } = storeToRefs(store);

// 活动状态监听相关
const hasShownPreDownloadPopup = ref(false);
const hasShownEndPopup = ref(false);
const checkTimer = ref<number | null>(null);

const contentTop = ref("38%");

// 预下载阶段时间（8小时 = 28800秒）
const PRE_DOWNLOAD_DURATION = 8 * 60 * 60;

// 存储初始时间基准
const initialServerTime = ref<number>(0);
const initialClientTime = ref<number>(0);

// 计算当前服务器时间
const getCurrentServerTime = computed(() => {
  if (!initialServerTime.value || !initialClientTime.value) return 0;

  // 计算当前服务器时间（服务器时间 + 客户端经过的时间）
  const currentClientTime = Date.now() / 1000;
  const elapsedTime = currentClientTime - initialClientTime.value;

  return Math.floor(initialServerTime.value + elapsedTime);
});

// 更新服务器时间基准
const updateServerTimeBase = () => {
  if (businessConfig.value.serverTime) {
    initialServerTime.value = businessConfig.value.serverTime;
    initialClientTime.value = Date.now() / 1000;
  }
};

// 判断活动状态
const getActivityStatus = () => {
  const endTime = warmPageInfo.value?.end_time;
  const currentServerTime = getCurrentServerTime.value;

  if (!endTime || !currentServerTime) {
    return { status: "unknown", isOnline: false };
  }

  const preDownloadStartTime = endTime - PRE_DOWNLOAD_DURATION;

  if (currentServerTime >= preDownloadStartTime && currentServerTime < endTime) {
    // 预下载阶段
    return { status: "pre-download", isOnline: false };
  } else if (currentServerTime >= endTime) {
    // 活动结束阶段
    return { status: "ended", isOnline: true };
  } else {
    // 活动进行中
    return { status: "active", isOnline: false };
  }
};

// 检查活动状态并触发弹窗
const checkActivityStatus = async () => {
  const { status, isOnline: newIsOnline } = getActivityStatus();

  // 调试信息
  const endTime = warmPageInfo.value?.end_time;
  const currentServerTime = getCurrentServerTime.value;
  const preDownloadStartTime = endTime ? endTime - PRE_DOWNLOAD_DURATION : 0;

  console.log("活动状态检查:", {
    status,
    endTime,
    currentServerTime,
    preDownloadStartTime,
    isOnline: newIsOnline,
    hasShownPreDownloadPopup: hasShownPreDownloadPopup.value,
    hasShownEndPopup: hasShownEndPopup.value,
  });

  // 更新 isOnline 状态
  isOnline.value = newIsOnline;

  // 根据状态触发弹窗
  if (status === "pre-download" && !hasShownPreDownloadPopup.value) {
    hasShownPreDownloadPopup.value = true;
    popup.value?.open();
    console.log("触发预下载阶段弹窗");
  } else if (status === "ended" && !hasShownEndPopup.value) {
    hasShownEndPopup.value = true;
    popup.value?.open();
    console.log("触发活动结束弹窗");
  }
};

// 开始监听活动状态
const startActivityMonitoring = () => {
  // 立即检查一次
  checkActivityStatus();

  // 每分钟检查一次状态
  checkTimer.value = window.setInterval(() => {
    checkActivityStatus();
  }, 60 * 1000); // 60秒检查一次
};

// 停止监听
const stopActivityMonitoring = () => {
  if (checkTimer.value) {
    clearInterval(checkTimer.value);
    checkTimer.value = null;
  }
};

// 监听数据变化
watch(
  [warmPageInfo, businessConfig],
  () => {
    if (warmPageInfo.value?.end_time && businessConfig.value.serverTime) {
      updateServerTimeBase();
      startActivityMonitoring();
    }
  },
  { immediate: true }
);

// 手动打开弹窗的方法（保持向后兼容）
const open = () => {
  popup.value?.open();
};

// 关闭弹窗
const handleClose = () => {
  popup.value?.close();
};

// 生命周期
onMounted(() => {
  if (warmPageInfo.value?.end_time && businessConfig.value.serverTime) {
    updateServerTimeBase();
    startActivityMonitoring();
  }
});

onUnmounted(() => {
  stopActivityMonitoring();
});

defineExpose({
  open,
});
</script>

<style scoped lang="less">
.bg {
  display: flex;
  justify-content: center;
  width: 701px;
  height: 523px;
  background: no-repeat 100%/100%;

  &.pre {
    background-image: url("@/assets/imgs/pop/event-end/pre-bg.png");
  }

  &.online {
    background-image: url("@/assets/imgs/pop/event-end/online-bg.png");
  }

  .btn {
    position: absolute;
    bottom: -15%;
    width: 264px;
    height: 80px;
    background: url("@/assets/imgs/pop/event-end/btn.png") no-repeat 100%/100%;
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #81638a);
    }
  }
}

@media (max-width: 540px) {
  .bg {
    width: 7.01rem;
    height: 5.23rem;

    .btn {
      width: 2.64rem;
      height: 0.8rem;

      &:hover {
        transform: scale(1.05);
      }
    }
  }
}
</style>
