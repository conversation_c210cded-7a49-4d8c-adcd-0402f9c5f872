<template>
  <div class="slide-content">
    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="title">
        <img src="@/assets/imgs/p4/title.png" />
      </div>
    </transition>

    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="reservation-count">
        <img src="@/assets/imgs/p4/reservation-count.png" alt="" />
        <div class="count">{{ reservationCount }}</div>
      </div>
    </transition>

    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="milestone-container">
        <!-- 动态里程碑图片 -->
        <div class="milestone-image">
          <img :src="milestoneImagesList[currentLevel]" :alt="`里程碑等级 ${currentLevel}`" />
          <!-- 奖品图片 -->
          <div v-if="currentLevel === 5" class="prize-image shine" @click="handlePrizeClick">
            <img class="breathe" src="@/assets/imgs/p4/prize.png" alt="奖品" />
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import { useDeviceDetection } from "@/hooks/useDeviceDetection";
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";

// Props 定义
interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
});

const store = getUseAppStore()();
const { warmPageInfo } = storeToRefs(store);

// 设备检测
const { isMobile } = useDeviceDetection();

// 响应式数据
const reservationCount = computed(() => warmPageInfo.value?.order_user_distinct || 0);

// 等级配置映射表
const levelThresholds = [
  { threshold: 0, level: 0 },
  { threshold: 100000, level: 1 },
  { threshold: 300000, level: 2 },
  { threshold: 500000, level: 3 },
  { threshold: 800000, level: 4 },
  { threshold: 1000000, level: 5 },
];

// 获取当前等级配置信息
const currentLevelInfo = computed(() => {
  const count = reservationCount.value;

  // 找到最后一个满足条件的等级配置
  const currentConfig = levelThresholds.filter((config) => count >= config.threshold).pop();

  return currentConfig || levelThresholds[0];
});

// 根据预约人数计算当前等级
const currentLevel = computed(() => currentLevelInfo.value.level);

// 批量导入PC端里程碑图片
const milestoneImagesRawPC = import.meta.glob("@/assets/imgs/p4/lv*.png", { eager: true });
// 批量导入移动端里程碑图片
const milestoneImagesRawMobile = import.meta.glob("@/assets/imgs/p4/m/lv*.png", { eager: true });

const milestoneImagesList = computed(() => {
  const imagesRaw = isMobile.value ? milestoneImagesRawMobile : milestoneImagesRawPC;
  return Object.values(imagesRaw).map((item: any) => item.default);
});

// 奖品图片点击处理
const handlePrizeClick = () => {
  store.toLink({ code: "community" });
};
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  position: absolute;
  top: 10px;
}

.reservation-count {
  position: absolute;
  top: 220px;
  display: flex;
  align-items: center;
  justify-content: center;

  .count {
    margin-left: 10px;
    font-size: 60px;
    font-weight: bold;
    background: linear-gradient(180deg, #fcfdfd 0%, #fefece 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 2px;
    position: relative;
    filter: drop-shadow(0 0 5px #333);

    /* 为不支持背景裁剪的浏览器提供备用方案 */
    color: #fcfdfd;
  }
}

.milestone-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 200px 0 0 20px;

  .milestone-image {
    position: relative;

    .prize-image {
      position: absolute;
      top: 240px;
      right: 25px;
      cursor: pointer;
    }
  }
}

// 移动端响应式适配
@media (max-width: 540px) {
  .slide-content {
    .title {
      top: 0.1rem;

      img {
        width: 4.5rem;
        height: auto;
      }
    }

    .reservation-count {
      top: 2.3rem;

      img {
        width: 3rem;
        height: auto;
      }

      .count {
        margin-left: 0.1rem;
        font-size: 0.5rem;
        letter-spacing: 0.02rem;
        filter: drop-shadow(0 0 0.05rem #333);
      }
    }

    .milestone-container {
      margin: 2rem 0 0 0;

      .milestone-image {
        img {
          width: 6.48rem;
          height: auto;
        }

        .prize-image {
          top: unset;
          bottom: 1.35rem;
          right: 1.21rem;

          img {
            width: 2rem;
            height: auto;
          }
        }
      }
    }
  }
}
</style>
