<template>
  <div class="slide-content">
    <ParticleEffect />

    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="title">
        <img src="@/assets/imgs/p5/title.png" />
      </div>
    </transition>

    <!-- 职业介绍主要内容 -->
    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="character-introduction">
        <!-- 左侧内容区域  -->
        <div class="left-section">
          <!-- 角色故事区域 -->
          <div class="character-story">
            <transition name="fade" mode="out-in">
              <img :key="selectedCharacter" :src="characterStoryImage" :alt="`角色${selectedCharacter}故事背景`" class="story-image" />
            </transition>
          </div>

          <!-- 职业切换按钮组 -->
          <div class="character-role-buttons">
            <button v-for="character in characters" :key="character.id" :class="['role-btn', { active: selectedCharacter === character.id }]" @click="selectCharacter(character.id)">
              <img :src="getRoleButtonImage(character.id)" :alt="`角色${character.id}职业图标`" class="role-icon" />
            </button>
          </div>
        </div>

        <!-- 右侧内容区域  -->
        <div class="right-section">
          <!-- 角色立绘 -->
          <div class="character-portrait">
            <WebM class="character-turntable" :src="WebMCharacterTurntable" />
            <transition name="slide-fade" mode="out-in">
              <img :key="selectedCharacter" :src="characterPortraitImage" :alt="`角色${selectedCharacter}立绘`" class="portrait-image" />
            </transition>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import WebM from "@/components/WebM.vue";
import ParticleEffect from "@/components/ParticleEffect.vue";

// WebM 资源导入
import WebMCharacterTurntable from "@/assets/imgs/webm/character-turntable.webm";

// Props 定义
interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean;
  initialCharacter?: number;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  initialCharacter: 1,
});

// 响应式数据
const selectedCharacter = ref(props.initialCharacter); // 当前选中的角色ID

// 角色数据数组
const characters = ref([
  { id: 1, name: "角色1" },
  { id: 2, name: "角色2" },
  { id: 3, name: "角色3" },
  { id: 4, name: "角色4" },
  { id: 5, name: "角色5" },
]);

// 计算属性：动态生成图片路径
const characterStoryImage = computed(() => {
  return new URL(`../../assets/imgs/p5/character-story/${selectedCharacter.value}.png`, import.meta.url).href;
});

const characterPortraitImage = computed(() => {
  return new URL(`../../assets/imgs/p5/character/${selectedCharacter.value}.png`, import.meta.url).href;
});

// 获取职业按钮图片的方法
const getRoleButtonImage = (characterId: number) => {
  const state = selectedCharacter.value === characterId ? "active" : "normal";
  return new URL(`../../assets/imgs/p5/character-role/${state}/${characterId}.png`, import.meta.url).href;
};

// 选择角色的方法
const selectCharacter = (characterId: number) => {
  selectedCharacter.value = characterId;
};
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
}

.character-introduction {
  position: absolute;
  top: 120px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  width: 100%;
  gap: 8%;

  // 左侧内容区域
  .left-section {
    display: flex;
    flex-direction: column;
    gap: 20px;

    .character-story {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-top: 30px;

      .story-image {
        width: 600px;
        height: 450px;
        object-fit: contain;
      }
    }

    .character-role-buttons {
      display: flex;
      justify-content: center;
      gap: 30px;

      .role-btn {
        background: none;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;

        &:hover {
          transform: scale(1.05);
          filter: brightness(1.1) drop-shadow(0 0 10px #81638a);
        }

        .role-icon {
          width: 119px;
          object-fit: contain;
          transition: all 0.3s ease;
        }
      }
    }
  }

  // 右侧内容区域
  .right-section {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-left: 20px;

    .character-portrait {
      position: relative;
      width: 500px;
      height: auto;
      display: flex;
      align-items: center;
      justify-content: center;

      .character-turntable {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 800px;
        height: 800px;
        transform: translate(-50%, -50%);
      }

      .portrait-image {
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.4));
      }
    }
  }
}

// 过渡动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.6s ease;
}

.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

// 移动端响应式适配
@media (max-width: 540px) {
  .slide-content {
    .title {
      top: 0.1rem;

      img {
        width: 3rem;
        height: auto;
      }
    }

    .character-introduction {
      top: 0.6rem;
      flex-direction: column;
      justify-content: flex-start;
      gap: 0.5rem;
      padding: 0 0.15rem;

      .left-section {
        width: 100%;

        .character-story {
          margin-top: 0.5rem;
          height: 4.5rem;

          .story-image {
            width: 6rem;
            height: 4.5rem;
          }
        }

        .character-role-buttons {
          position: absolute;
          bottom: 0.2rem;
          left: 50%;
          transform: translateX(-50%);
          gap: 0.3rem;
          z-index: 1;

          .role-btn {
            overflow: visible;

            .role-icon {
              width: 0.8rem;
            }
          }
        }
      }

      .right-section {
        position: absolute;
        top: 4.8rem;
        left: 50%;
        width: 100%;
        padding-left: 0;
        transform: translateX(-50%);

        .character-portrait {
          height: 6.2rem;

          .character-turntable {
            width: 6.5rem;
            height: 6.5rem;
          }

          .portrait-image {
            display: block;
            width: 4rem;
            height: auto;
            max-width: unset;
            max-height: unset;
            filter: drop-shadow(0 0.1rem 0.3rem rgba(0, 0, 0, 0.4));
          }
        }
      }
    }
  }

  // 过渡动画适配
  .slide-fade-enter-from {
    transform: translateX(0.3rem);
  }

  .slide-fade-leave-to {
    transform: translateX(-0.3rem);
  }
}
</style>
