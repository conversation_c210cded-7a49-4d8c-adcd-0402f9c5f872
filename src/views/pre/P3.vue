<template>
  <div class="slide-content">
    <ParticleEffect />
    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="title">
        <img src="@/assets/imgs/p3/title.png" alt="" />
      </div>
    </transition>
    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="lottery">
        <WebM :src="WebMLottery" />
        <div class="lottery-btn" @click="openLotteryPopup">
          <img class="breathe" src="@/assets/imgs/p3/lottery-btn.png" alt="" />
        </div>
      </div>
    </transition>
    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="rule-btn" @click="openRulesPopup">
        <img src="@/assets/imgs/p3/rule-btn.png" alt="" />
      </div>
    </transition>
    <teleport to="body">
      <!-- 抽奖弹窗 -->
      <LotteryPopup ref="lotteryPopRef" />
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import WebM from "@/components/WebM.vue";
import ParticleEffect from "@/components/ParticleEffect.vue";
import LotteryPopup from "@/components/popup/LotteryPopup.vue";
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from "@/config/prizeConfig";

// WebM 资源导入
import WebMLottery from "@/assets/imgs/webm/lottery.webm";

// Props 定义
interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean;
  rulesPopRef?: any;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  rulesPopRef: null,
});

const store = getUseAppStore()();
const { orderPhone } = storeToRefs(store);

// Pop
const lotteryPopRef = ref<InstanceType<typeof LotteryPopup> | null>(null);

// 打开规则弹窗
const openRulesPopup = () => {
  if (props.rulesPopRef) {
    props.rulesPopRef.open();
  }
};

// 打开抽奖弹窗
const openLotteryPopup = () => {
  store.ga4TrackEvent("bellclick");
  if (!orderPhone.value) {
    store.showAlert("먼저 사전예약 STEP1를 진행하세요.");
    return;
  }
  store.grant(prizeConfig.zykr_event_growup.id);
  lotteryPopRef.value?.open();
};
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  position: absolute;
  top: 10px;
  z-index: 1;
}

.lottery {
  position: absolute;
  top: 10px;
  width: 1920px;

  .lottery-btn {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    cursor: pointer;
    z-index: 1;
  }
}

.rule-btn {
  position: absolute;
  bottom: 50px;
  cursor: pointer;
}

// 移动端响应式适配
@media (max-width: 540px) {
  .slide-content {
    .title {
      top: 0.1rem;

      img {
        width: 6rem;
        height: auto;
      }
    }

    .lottery {
      top: 0.1rem;
      width: 23rem;

      .lottery-btn {
        img {
          width: 2rem;
          height: auto;
        }
      }
    }

    .rule-btn {
      bottom: 2rem;

      img {
        width: 1rem;
        height: auto;
      }
    }
  }
}
</style>
