<template>
  <div class="slide-content">
    <transition name="title-slide-fade" appear>
      <div v-if="props.isActive" class="title">
        <img src="@/assets/imgs/p6/title.png" />
      </div>
    </transition>

    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="btns">
        <div class="btn create" @click="handleOpenCreateGuild">
          <img src="@/assets/imgs/p6/create-guild-btn.png" alt="" />
        </div>
        <div class="btn join" @click="handleOpenJoinGuild">
          <img src="@/assets/imgs/p6/join-guild-btn.png" alt="" />
        </div>
      </div>
    </transition>

    <transition name="content-slide-fade" appear>
      <div v-if="props.isActive" class="content">
        <div class="guild-list">
          <div class="guild-item" v-for="guild in currentPageGuilds" :key="guild.id">
            <div class="info">
              <span class="name">{{ guild.name }}</span>
              <span class="link">{{ guild.type }}</span>
            </div>
            <transition name="slide-fade" mode="out-in">
              <div v-if="isJoinGuild" class="btn" :class="{ full: guild.is_full }" @click="handleJoinGuild(guild)"></div>
            </transition>
          </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
          <button class="pagination-btn prev-btn" :disabled="isPrevDisabled" @click="goToPrevPage">
            <img src="@/assets/imgs/p6/right-arrow.png" alt="上一页" class="arrow-icon prev-arrow" />
          </button>

          <!-- 页码按钮组 -->
          <div class="page-numbers">
            <span v-for="page in totalPages" :key="page" class="page-number-btn" :class="{ active: currentPage === page }" @click="goToPage(page)">
              {{ page }}
            </span>
          </div>

          <button class="pagination-btn next-btn" :disabled="isNextDisabled" @click="goToNextPage">
            <img src="@/assets/imgs/p6/right-arrow.png" alt="下一页" class="arrow-icon next-arrow" />
          </button>
        </div>
      </div>
    </transition>
    <!-- 公会创建弹窗 -->
    <teleport to="body">
      <GuildCreatePopup ref="guildCreatePopRef" @create-guild="handleCreateGuild" />
    </teleport>

    <teleport to="body">
      <!-- 公会奖励弹窗 -->
      <GuildPrizePopup ref="guildPrizePopRef" />
    </teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";
import { getUseAppStore } from "@kingnet/kingnet-pre-test";
import { storeToRefs } from "pinia";
import { prizeConfig } from "@/config/prizeConfig";
import GuildCreatePopup from "@/components/popup/GuildCreatePopup.vue";
import GuildPrizePopup from "@/components/popup/GuildPrizePopup.vue";

// Props 定义
interface Guild {
  id: number;
  name: string;
  type: string;
  is_full: boolean;
}

interface Props {
  /** 当前屏幕是否为激活状态 */
  isActive?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
});

const store = getUseAppStore()();
const { orderPhone } = storeToRefs(store);

watch(
  orderPhone,
  (phone) => {
    if (!phone) return;
    fetchGuilds();
  },
  { immediate: true }
);

// Pop
const guildCreatePopRef = ref<InstanceType<typeof GuildCreatePopup> | null>(null);
const guildPrizePopRef = ref<InstanceType<typeof GuildPrizePopup> | null>(null);

// 响应式数据
const guilds = ref<Guild[]>([]);
const isJoinGuild = ref(false);
const currentPage = ref(1); // 当前页码
const pageSize = 4; // 每页显示数量
const totalPages = computed(() => Math.ceil(guilds.value.length / pageSize)); // 总页数

// 计算当前页显示的公会列表
const currentPageGuilds = computed(() => {
  const startIndex = (currentPage.value - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  return guilds.value.slice(startIndex, endIndex);
});

function notPhoneTips() {
  if (!orderPhone.value) {
    store.showAlert("먼저 사전예약 STEP1를 진행하세요.");
    return false;
  }
  return true;
}

// 获取公会列表
async function fetchGuilds() {
  const { phone, areaCode, businessConfig } = storeToRefs(store);
  const res = await store.ApiPost("/website/camps/getRandom", {
    phone: phone.value,
    dialing_code: areaCode.value,
    en_name: businessConfig.value.en_name,
  });
  if (res.code === 0) {
    guilds.value = res.data.map((item: any) => ({
      id: item.camp_id,
      name: item.camp_name,
      type: item.camp_type,
      is_full: item.is_full === 1,
    }));
  }
}

// 分页控制方法
const goToPrevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

// 判断按钮是否禁用
const isPrevDisabled = computed(() => currentPage.value === 1);
const isNextDisabled = computed(() => currentPage.value === totalPages.value);

// 跳转到指定页面
const goToPage = (page: number) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

// 打开创建公会弹窗
const handleOpenCreateGuild = () => {
  store.ga4TrackEvent("foundguild");
  if (!notPhoneTips()) return;
  guildCreatePopRef.value?.open();
};

// 创建完成公会
const handleCreateGuild = (data: any) => {
  handleOpenGuildPrizePopup(data.social_link);
  fetchGuilds();
};

// 打开加入公会开关
const handleOpenJoinGuild = () => {
  store.ga4TrackEvent("joinguild");
  if (!notPhoneTips()) return;
  isJoinGuild.value = !isJoinGuild.value;
};

// 加入公会
const handleJoinGuild = async (guild: Guild) => {
  if (guild.is_full) return;
  store.ga4TrackEvent("joinguild");
  const { phone, areaCode, businessConfig } = storeToRefs(store);
  const res = await store.ApiPost("/website/camps/joinCamp", {
    camp_id: guild.id,
    phone: phone.value,
    dialing_code: areaCode.value,
    en_name: businessConfig.value.en_name,
  });
  if (res.code === 0) {
    handleOpenGuildPrizePopup(res.data.social_link);
    fetchGuilds();
  } else {
    store.showAlert(res.message);
  }
};

// 打开公会奖励弹窗
const handleOpenGuildPrizePopup = (link: string) => {
  store.grant(prizeConfig.zykr_guild_growup.id);
  guildPrizePopRef.value?.open(link);
};
</script>

<style lang="less" scoped>
.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.title {
  position: absolute;
  top: 10px;
}

.btns {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 130px 0 0;
  gap: 50px;
  z-index: 1;

  .btn {
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: scale(1.05);
      filter: brightness(1.1) drop-shadow(0 0 10px #b68bc3);
    }

    &.join {
      &:hover {
        filter: brightness(1.1) drop-shadow(0 0 10px #b4e0ff);
      }
    }
  }
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 100px;
  width: 1036px;
  height: 588px;
  background: url(@/assets/imgs/p6/box-bg.png) no-repeat 100%/100%;
  color: #fff;

  .guild-list {
    display: flex;
    align-items: center;
    flex-direction: column;
    min-height: 300px;
    gap: 30px;
    margin-bottom: 40px;
  }

  .guild-item {
    display: flex;
    align-items: center;
    gap: 20px;

    .info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 817px;
      height: 51px;
      background: url(@/assets/imgs/p6/guild-bg.png);

      span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 49%;
        height: 100%;
        font-size: 18px;
      }

      .link {
        width: 48%;
      }
    }

    .btn {
      width: 100px;
      height: 35px;
      background: url(@/assets/imgs/p6/join-btn.png);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(.full) {
        transform: scale(1.05);
        filter: brightness(1.1);
      }

      &.full {
        background-image: url(@/assets/imgs/p6/can-not-join-btn.png);
        cursor: not-allowed;
      }
    }
  }

  // 分页控件样式
  .pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-top: 20px;

    .pagination-btn {
      background: none;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;
      padding: 8px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover:not(:disabled) {
        transform: scale(1.1);
      }

      &:disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }

      .arrow-icon {
        width: 21px;
        height: 9px;
        transition: all 0.3s ease;

        &.prev-arrow {
          transform: scaleX(-1);
        }

        &.next-arrow {
          transform: scaleX(1);
        }
      }
    }

    // 页码按钮组样式
    .page-numbers {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;

      .page-number-btn {
        width: 40px;
        height: 40px;
        color: #a7a7a7;
        cursor: pointer;
        font-size: 24px;
        font-weight: bold;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          color: #fff;
          transform: scale(1.2);
        }

        &.active {
          color: #fff;
          filter: drop-shadow(0 0 10px #fff);
        }
      }
    }
  }
}

// 过渡动画效果
.slide-fade-enter-active {
  transition: all 0.6s ease;
}

.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}

// 移动端响应式适配
@media (max-width: 540px) {
  .slide-content {
    .title {
      top: 0.1rem;

      img {
        width: 4rem;
        height: auto;
      }
    }

    .btns {
      margin: 2rem 0 0;
      gap: 0;
      flex-direction: column;

      .btn {
        margin-top: -0.4rem;

        img {
          width: 4.5rem;
          height: auto;
        }

        &.create {
          margin-left: -0.5rem;
        }

        &.join {
          margin-left: 0.5rem;
        }
      }
    }

    .content {
      justify-content: space-between;
      padding: 1.8rem 0 0.8rem;
      width: 6.4rem;
      height: 7.72rem;
      background: url(@/assets/imgs/p6/m/box-bg.png) no-repeat 100%/100%;

      .guild-list {
        min-height: 3rem;
        gap: 0.3rem;
        margin-bottom: 0.4rem;
        margin-left: 0.35rem;
        align-self: flex-start;
      }

      .guild-item {
        gap: 0.2rem;

        .info {
          width: 4.6rem;
          height: 0.76rem;
          background: url(@/assets/imgs/p6/m/guild-bg.png) no-repeat 100%/100%;

          span {
            width: 45.5%;
            font-size: 0.18rem;
          }

          .link {
            width: 49.5%;
          }
        }

        .btn {
          width: 1rem;
          height: 0.35rem;
          background-size: contain;
        }
      }

      .pagination {
        gap: 0.3rem;
        margin-top: 0.2rem;

        .pagination-btn {
          padding: 0.08rem;

          .arrow-icon {
            width: 0.21rem;
            height: 0.09rem;
          }
        }

        .page-numbers {
          gap: 0.15rem;

          .page-number-btn {
            width: 0.4rem;
            height: 0.4rem;
            font-size: 0.24rem;
          }
        }
      }
    }
  }

  // 过渡动画适配
  .slide-fade-enter-from {
    transform: translateX(0.3rem);
  }

  .slide-fade-leave-to {
    transform: translateX(-0.3rem);
  }
}
</style>
