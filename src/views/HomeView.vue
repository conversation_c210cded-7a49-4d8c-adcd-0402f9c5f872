<template>
  <!-- 顶部Header - 等待初始化完成后显示 -->
  <div v-if="isInitialized" class="home-view">
    <Header @reward-click="openPrizeListPopup" />

    <!-- 侧边栏导航 - 仅PC端显示 -->
    <SidebarNavigation v-if="!isMobile" :current-slide="currentSlide" :active-tab="activeTab" :main-swiper-instance="mainSwiperInstance" @slide-change="onSlideChange" @tab-change="switchTab" />

    <!-- 右侧悬浮菜单 - 仅PC端显示 -->
    <RightFloatingMenu v-if="!isMobile" :main-swiper-instance="mainSwiperInstance" />

    <!-- PC端：垂直滚屏容器 -->
    <VerticalSwiper v-if="!isMobile" custom-class="main-swiper" @slide-change="onSlideChange" @swiper-ready="onMainSwiperReady">
      <!-- 第1屏 - 首页 -->
      <swiper-slide class="slide-item slide-1">
        <P1 :is-active="currentSlide === 0" :main-swiper-instance="mainSwiperInstance" />
      </swiper-slide>

      <!-- 第2屏 - 预约 -->
      <swiper-slide class="slide-item slide-2">
        <P2 :is-active="currentSlide === 1" @open-privacy-popup="openPrivacyPopup" />
      </swiper-slide>

      <!-- 第3屏 - 抽奖 -->
      <swiper-slide class="slide-item slide-3">
        <P3 :is-active="currentSlide === 2" :rules-pop-ref="rulesPopRef" />
      </swiper-slide>

      <!-- 第4屏 - 里程碑 -->
      <swiper-slide class="slide-item slide-4">
        <P4 :is-active="currentSlide === 3" :reservation-count="reservationCount" :current-level="currentLevel" />
      </swiper-slide>

      <!-- 第5屏 - 职业介绍 -->
      <swiper-slide class="slide-item slide-5">
        <P5 :is-active="currentSlide === 4" :initial-character="selectedCharacter" />
      </swiper-slide>

      <!-- 第6屏 - 公会 -->
      <swiper-slide class="slide-item slide-6">
        <P6 :is-active="currentSlide === 5" />
      </swiper-slide>

      <!-- 第7屏 - 游戏世界观介绍 -->
      <swiper-slide class="slide-item slide-7">
        <P7 :is-active="currentSlide === 6" :initial-tab="activeTab" @tab-change="switchTab" />
      </swiper-slide>
    </VerticalSwiper>

    <!-- 移动端：线性布局 -->
    <MobileLayout
      v-if="isMobile"
      :rules-pop-ref="rulesPopRef"
      :reservation-count="reservationCount"
      :current-level="currentLevel"
      :selected-character="selectedCharacter"
      :active-tab="activeTab"
      @tab-change="switchTab"
      @open-privacy-popup="openPrivacyPopup"
    />

    <!-- 第2屏到第3屏过渡动画 -->
    <TransitionAnimation ref="transitionAnimationRef" @animation-start="onAnimationStart" @animation-end="onAnimationEnd" />

    <!-- 活动规则弹窗 -->
    <RulesPopup ref="rulesPopRef" />

    <!-- 个人信息隐私收集弹窗 -->
    <PrivacyPopup ref="privacyPopRef" />

    <!-- 奖励列表弹窗 -->
    <PrizeListPopup ref="prizeListPopRef" :main-swiper-instance="mainSwiperInstance" />

    <!-- 活动结束弹窗 -->
    <EventEndPopup ref="eventEndPopRef" />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { SwiperSlide } from "swiper/vue";

// 导入自定义组件
import Header from "@/components/Header.vue";
import VerticalSwiper from "@/components/swiper/VerticalSwiper.vue";
import SidebarNavigation from "@/components/SidebarNavigation.vue";
import RightFloatingMenu from "@/components/RightFloatingMenu.vue";
import RulesPopup from "@/components/popup/RulesPopup.vue";
import PrivacyPopup from "@/components/popup/PrivacyPopup.vue";
import PrizeListPopup from "@/components/popup/PrizeListPopup.vue";
import TransitionAnimation from "@/components/TransitionAnimation.vue";
import EventEndPopup from "@/components/popup/EventEndPopup.vue";

// Hooks
import { useDeviceDetection } from "@/hooks/useDeviceDetection";
import { useWarmupPage } from "@/hooks/useWarmupPage";

// 导入页面组件
import P1 from "@/views/pre/P1.vue";
import P2 from "@/views/pre/P2.vue";
import P3 from "@/views/pre/P3.vue";
import P4 from "@/views/pre/P4.vue";
import P5 from "@/views/pre/P5.vue";
import P6 from "@/views/pre/P6.vue";
import P7 from "@/views/pre/P7.vue";
import MobileLayout from "@/views/pre/MobileLayout.vue";

// 初始化状态
const isInitialized = ref(false);

// 预热页初始化
useWarmupPage().then(() => {
  isInitialized.value = true;
});

// Pop
const rulesPopRef = ref<InstanceType<typeof RulesPopup> | null>(null);
const privacyPopRef = ref<InstanceType<typeof PrivacyPopup> | null>(null);
const prizeListPopRef = ref<InstanceType<typeof PrizeListPopup> | null>(null);
const transitionAnimationRef = ref<InstanceType<typeof TransitionAnimation> | null>(null);
const eventEndPopRef = ref<InstanceType<typeof EventEndPopup> | null>(null);

// 设备检测
const { isMobile } = useDeviceDetection();

// 响应式数据
const currentSlide = ref(0);
const activeTab = ref("tab1");
const mainSwiperInstance = ref<any>(null);

// 第4屏里程碑等级控制
const currentLevel = ref(0); // 0-5 对应 lv0.png 到 lv5.png
const reservationCount = ref(123456);

// 第5屏职业介绍相关数据
const selectedCharacter = ref(1); // 当前选中的角色ID

// 主 Swiper 事件处理
const onSlideChange = (index: number) => {
  const previousSlide = currentSlide.value;
  currentSlide.value = index;

  // 检查是否从第2屏切换到第3屏
  if (previousSlide === 1 && index === 2) {
    transitionAnimationRef.value?.playTransitionAnimation();
  }
};

const onMainSwiperReady = (swiper: any) => {
  mainSwiperInstance.value = swiper;
};

// Tab 切换功能
const switchTab = (tab: string) => {
  activeTab.value = tab;
};

// 过渡动画事件处理
const onAnimationStart = () => {
  console.log("过渡动画开始播放");
};

const onAnimationEnd = () => {
  console.log("过渡动画播放结束");
};

// 打开隐私政策弹窗
const openPrivacyPopup = () => {
  privacyPopRef.value?.open();
};

// 打开奖品列表弹窗
const openPrizeListPopup = () => {
  prizeListPopRef.value?.open();
};
</script>

<style lang="less" scoped>
.home-view {
  width: 100%;
  height: 100vh;
  position: relative;
}

// 粒子效果
.particle {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 0;
}

.slide-item {
  width: 100%;
  height: 100vh;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;

  .title {
    position: absolute;
    top: 10px;
  }

  // 为每个屏幕设置背景图
  &.slide-1 {
    background-image: url("@/assets/imgs/bg/p1.jpg");
  }

  &.slide-2 {
    background-image: url("@/assets/imgs/bg/p2.jpg");
  }

  &.slide-3 {
    background-image: url("@/assets/imgs/bg/p3.jpg");
  }

  &.slide-4 {
    background-image: url("@/assets/imgs/bg/p4.jpg");
  }

  &.slide-5 {
    background-image: url("@/assets/imgs/bg/p5.jpg");
  }

  &.slide-6 {
    background-image: url("@/assets/imgs/bg/p6.jpg");
  }

  &.slide-7 {
    background-color: #000;
  }
}

.slide-content {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

// 过渡动画效果
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.6s ease;
}

.slide-fade-leave-active {
  transition: all 0.4s ease;
}

.slide-fade-enter-from {
  transform: translateX(30px);
  opacity: 0;
}

.slide-fade-leave-to {
  transform: translateX(-30px);
  opacity: 0;
}
</style>
