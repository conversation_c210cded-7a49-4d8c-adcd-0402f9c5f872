import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory("/"),
  routes: [
    {
      path: "/",
      component: () => import("../views/HomeView.vue"),
    },
    // {
    //   path: "/",
    //   component: () => import("../views/Official.vue"),
    // },
    {
      path: "/reserve/:recommend_utm?",
      name: "reserve",
      component: () => import("../views/HomeView.vue"),
    },
    // {
    //   path: "/shorts1/:recommend_utm?",
    //   name: "shorts1",
    //   component: () => import("../views/shorts1/index.vue"),
    // },
  ],
});

export default router;
